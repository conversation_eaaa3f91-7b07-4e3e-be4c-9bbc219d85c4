# Image Storage Implementation

This document describes the shared drive image storage implementation for user-uploaded images in chat conversations.

## Overview

The system now saves user-uploaded images to a shared drive with a structured directory layout, while maintaining the existing functionality for vision models and OCR processing.

## Directory Structure

Images are saved with the following structure:
```
{BASE64_FILE_PATH}/YYYY-MM-DD/{ssoid}/uuid-filename.jpg
```

Example:
```
# Production
/mnt/shared-drive/genai/chat/2025-01-08/user123/550e8400-e29b-41d4-a716-************-screenshot.png

# Testing (Downloads folder)
C:\Users\<USER>\Downloads\2025-01-08\user123\550e8400-e29b-41d4-a716-************-screenshot.png
```

## Configuration

### Environment Variables

Add to your `.env` file:

```bash
# Required: Path to shared drive for storing chat images (reuses existing BASE64_FILE_PATH)
# Production example:
BASE64_FILE_PATH=/path/to/shared/drive/genai/chat

# Testing example (using Downloads folder):
BASE64_FILE_PATH=C:\Users\<USER>\Downloads

# Optional: Encryption key for image files (currently not implemented)
# BASE64_FILE_ENCRYPTION_KEY=your-encryption-key-here
```

## Implementation Details

### Services

1. **ImageStorageService** (`apps/api/src/utils/image-storage.service.ts`)
   - Handles saving images to shared drive
   - Manages directory creation and file naming
   - Provides image retrieval and deletion methods
   - Security validation for user access

2. **ImageController** (`apps/api/src/general/image.controller.ts`)
   - REST endpoint for retrieving stored images
   - URL: `GET /api/general/images/{imagePath}`
   - Includes security checks and proper content-type headers

3. **ChatCompletionService** (updated)
   - Modified to save images to shared drive during file processing
   - Stores image paths in database message content
   - Maintains existing vision model and OCR functionality

### Database Storage

Images are referenced in the database message content as:
```
User prompt text [Files: document.pdf | Images: 2025-01-08/user123/uuid-image.jpg]
```

### API Endpoints

#### Save Images (Automatic)
Images are automatically saved during chat completion requests with file uploads.

#### Retrieve Images
```
GET /api/general/images/{imagePath}
```

Example:
```
GET /api/general/images/2025-01-08/user123/550e8400-e29b-41d4-a716-************-screenshot.png
```

## Security Features

1. **User Validation**: Image paths must contain the requesting user's SSO ID
2. **Path Validation**: Prevents directory traversal attacks
3. **Authentication**: Requires valid user authentication
4. **Access Control**: Users can only access their own images

## File Processing Flow

1. **Upload**: User uploads image via chat interface
2. **Save**: Image saved to shared drive with UUID-based filename
3. **Process**: 
   - Vision models: Image sent as base64 to LLM API
   - Non-vision models: Image processed with OCR for text extraction
4. **Store**: Image path stored in database message content
5. **Retrieve**: Images accessible via REST API for display

## Error Handling

- **Missing Configuration**: Throws error if `CHAT_IMAGE_STORAGE_PATH` not set
- **File System Errors**: Proper error logging and user-friendly messages
- **Access Denied**: Security validation prevents unauthorized access
- **File Not Found**: Graceful handling of missing images

## Future Enhancements

1. **Encryption**: Add optional file encryption using `CHAT_IMAGE_ENCRYPTION_KEY`
2. **Cleanup**: Implement automatic cleanup of old images
3. **Compression**: Add image compression for storage optimization
4. **Thumbnails**: Generate thumbnails for faster loading
5. **Cloud Storage**: Support for cloud storage providers (S3, Azure Blob, etc.)

## Migration from Old Repository

The implementation is based on the old repository's `Base64MediaHandler.tsx` but with improvements:

- **Server-side processing**: More secure than client-side handling
- **Better error handling**: Structured error responses
- **Type safety**: Full TypeScript implementation
- **Modular design**: Separated concerns with dedicated services
- **REST API**: Proper REST endpoints for image retrieval

## Testing

To test the implementation:

1. Set `BASE64_FILE_PATH` in your environment
2. Upload an image in a chat conversation
3. Check that the image is saved to the shared drive
4. Verify the image path is stored in the database
5. Test image retrieval via the REST API

## Troubleshooting

### Common Issues

1. **Permission Denied**: Ensure the application has write access to `BASE64_FILE_PATH`
2. **Path Not Found**: Verify the shared drive path exists and is accessible
3. **Large Files**: Check file size limits in your web server configuration
4. **Network Drive**: Ensure network drives are properly mounted and accessible

### Logs

Check application logs for detailed error information:
- Image save operations are logged at INFO level
- Errors are logged at ERROR level with full stack traces
- Debug information available at DEBUG level
